import { deleteCustomerPost } from '@/lib/actions/customerPosts';
import { createClient } from '@/utils/supabase/server';
import { deleteCustomerPostMedia } from '@/lib/actions/shared/delete-customer-post-media';

// Mock dependencies
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn()
}));

jest.mock('@/lib/actions/shared/delete-customer-post-media', () => ({
  deleteCustomerPostMedia: jest.fn()
}));

const { createClient } = require('@/utils/supabase/client');
const { deleteCustomerPostMedia } = require('@/lib/actions/shared/delete-customer-post-media');

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>;
const mockDeleteCustomerPostMedia = deleteCustomerPostMedia as jest.MockedFunction<typeof deleteCustomerPostMedia>;

describe('deleteCustomerPost with media deletion', () => {
  const mockUser = { id: 'user-123' };
  const mockPostId = 'post-123';
  const mockCreatedAt = '2024-01-01T00:00:00Z';

  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();

    mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null
        })
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn()
          })
        }),
        delete: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({ error: null })
        })
      })
    };

    mockCreateClient.mockResolvedValue(mockSupabase);
  });

  it('should delete post successfully after media deletion', async () => {
    // Mock post exists
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: { id: mockPostId, created_at: mockCreatedAt, image_url: 'https://example.com/image.jpg' },
      error: null
    });

    // Mock successful media deletion
    mockDeleteCustomerPostMedia.mockResolvedValue({ success: true });

    const result = await deleteCustomerPost(mockPostId);

    expect(result).toEqual({
      success: true,
      message: 'Post deleted successfully'
    });

    expect(mockDeleteCustomerPostMedia).toHaveBeenCalledWith(mockUser.id, mockPostId, mockCreatedAt);
    expect(mockSupabase.from).toHaveBeenCalledWith('customer_posts');
    expect(mockSupabase.from().delete().eq).toHaveBeenCalledWith('id', mockPostId);
  });

  it('should not delete post when media deletion fails', async () => {
    // Mock post exists
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: { id: mockPostId, created_at: mockCreatedAt, image_url: 'https://example.com/image.jpg' },
      error: null
    });

    // Mock failed media deletion
    mockDeleteCustomerPostMedia.mockResolvedValue({
      success: false,
      error: 'Failed to delete images from storage'
    });

    const result = await deleteCustomerPost(mockPostId);

    expect(result).toEqual({
      success: false,
      message: 'Failed to delete post images',
      error: 'Cannot delete post: Failed to delete images from storage'
    });

    expect(mockDeleteCustomerPostMedia).toHaveBeenCalledWith(mockUser.id, mockPostId, mockCreatedAt);
    // Post deletion should not be attempted
    expect(mockSupabase.from().delete).not.toHaveBeenCalled();
  });

  it('should not delete post when media deletion throws error', async () => {
    // Mock post exists
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: { id: mockPostId, created_at: mockCreatedAt, image_url: null },
      error: null
    });

    // Mock media deletion throws error
    mockDeleteCustomerPostMedia.mockRejectedValue(new Error('Storage service unavailable'));

    const result = await deleteCustomerPost(mockPostId);

    expect(result).toEqual({
      success: false,
      message: 'Failed to delete post images',
      error: 'Cannot delete post: Failed to clean up associated images'
    });

    expect(mockDeleteCustomerPostMedia).toHaveBeenCalledWith(mockUser.id, mockPostId, mockCreatedAt);
    // Post deletion should not be attempted
    expect(mockSupabase.from().delete).not.toHaveBeenCalled();
  });

  it('should handle posts without images correctly', async () => {
    // Mock post exists without image
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: { id: mockPostId, created_at: mockCreatedAt, image_url: null },
      error: null
    });

    // Mock successful media deletion (should still clean up folder)
    mockDeleteCustomerPostMedia.mockResolvedValue({ success: true });

    const result = await deleteCustomerPost(mockPostId);

    expect(result).toEqual({
      success: true,
      message: 'Post deleted successfully'
    });

    // Should still attempt to clean up folder even if no image_url
    expect(mockDeleteCustomerPostMedia).toHaveBeenCalledWith(mockUser.id, mockPostId, mockCreatedAt);
    expect(mockSupabase.from().delete().eq).toHaveBeenCalledWith('id', mockPostId);
  });

  it('should return error when post not found', async () => {
    // Mock post not found
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: null,
      error: { message: 'Post not found' }
    });

    const result = await deleteCustomerPost(mockPostId);

    expect(result).toEqual({
      success: false,
      message: 'Post not found',
      error: 'The post does not exist or you do not have permission to delete it'
    });

    // Should not attempt media or post deletion
    expect(mockDeleteCustomerPostMedia).not.toHaveBeenCalled();
    expect(mockSupabase.from().delete).not.toHaveBeenCalled();
  });

  it('should return error when user not authenticated', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: { message: 'Not authenticated' }
    });

    const result = await deleteCustomerPost(mockPostId);

    expect(result).toEqual({
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to delete a post'
    });

    expect(mockDeleteCustomerPostMedia).not.toHaveBeenCalled();
    expect(mockSupabase.from().delete).not.toHaveBeenCalled();
  });

  it('should handle database deletion errors after successful media deletion', async () => {
    // Mock post exists
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: { id: mockPostId, created_at: mockCreatedAt, image_url: 'https://example.com/image.jpg' },
      error: null
    });

    // Mock successful media deletion
    mockDeleteCustomerPostMedia.mockResolvedValue({ success: true });

    // Mock database deletion failure
    mockSupabase.from().delete().eq.mockResolvedValue({
      error: { message: 'Database error' }
    });

    const result = await deleteCustomerPost(mockPostId);

    expect(result).toEqual({
      success: false,
      message: 'Failed to delete post',
      error: 'Database error'
    });

    expect(mockDeleteCustomerPostMedia).toHaveBeenCalledWith(mockUser.id, mockPostId, mockCreatedAt);
    expect(mockSupabase.from().delete().eq).toHaveBeenCalledWith('id', mockPostId);
  });
});
